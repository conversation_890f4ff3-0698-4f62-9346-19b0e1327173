# Connectivity Mapper (CMap) v01

A comprehensive Igor Pro package for analyzing synaptic connectivity in neural circuits using two-photon (2P) optogenetic stimulation and electrophysiology data.

## Overview

The Connectivity Mapper (CMap) is a sophisticated analysis tool developed by <PERSON><PERSON> for mapping synaptic connections in neural tissue. It combines two-photon imaging data with electrophysiological recordings to create detailed connectivity maps and analyze synaptic properties across cortical layers and radial distances.

## Key Features

### Data Integration
- **Automated Data Loading**: Loads both 2P imaging data and electrophysiology recordings
- **Coordinate System Management**: Handles stage coordinates (X, Y, Z) and converts between image and layout coordinate systems
- **Multi-FOV Analysis**: Processes multiple fields of view (FOV) with automatic image coordinate extraction

### Connectivity Analysis
- **Layer-wise Analysis**: Analyzes connectivity patterns across cortical layers (L2/3, L4, L5, L6)
- **Radial Analysis**: Examines connectivity as a function of distance from the postsynaptic cell
- **Column Analysis**: Studies connectivity within cortical columns
- **3D Spatial Mapping**: Creates three-dimensional connectivity maps with Z-coordinate support

### Synaptic Response Characterization
- **EPSP Amplitude Analysis**: Measures excitatory postsynaptic potential amplitudes
- **Paired-Pulse Ratio (PPR)**: Calculates short-term plasticity metrics
- **Triple-Pulse Ratio (TPR)**: Analyzes responses to three consecutive stimuli
- **Maximum Depolarization**: Tracks peak depolarization and latency
- **Direct Activation Detection**: Identifies and handles direct optogenetic activation

### Visualization and Statistics
- **Heat Maps**: Creates connectivity and amplitude heat maps with gamma correction
- **Layer Statistics Plots**: Generates bar graphs, box plots, and violin plots
- **3D Scatter Plots**: Visualizes connectivity in three-dimensional space
- **Statistical Analysis**: Performs t-tests and Wilcoxon rank tests for significance testing

## Installation

1. Place the `cmap_v01.ipf` file in your Igor Pro User Procedures folder
2. Restart Igor Pro or compile the procedure file
3. Access the tools through the "Macros" menu

## Quick Start

### Basic Workflow

1. **Initialize the System**
   ```
   Macros → Init Connectivity Mapper
   ```

2. **Set Image Path**
   - Click "Set the image path" to specify your data directory
   - Ensure your images contain TIFF metadata with stage coordinates

3. **Load Data**
   - Click "Load all" to automatically load images and extract coordinates
   - The system will create a parameter table for your experiment

4. **Create Layout**
   - Click "Make layout" to generate the spatial layout of your experiment
   - Adjust scale as needed (default 50%)

5. **Analyze Connectivity**
   - Use "Pull connectivity from JT Load Waves" to import response data
   - Click "2p Zap -> layout" to map stimulation points to the layout
   - Run "Layer stats" for comprehensive analysis

### Data Table Configuration

The parameter table includes:
- **Image Name**: TIFF file names
- **Stage Coordinates**: X, Y, Z positions from TIFF metadata
- **Pixels Per Micron**: Scaling factor for each image
- **Image/Ephys Start/End**: Frame ranges for analysis

## Analysis Parameters

### Thresholds
- **EPSP Threshold for PPR**: Minimum amplitude for paired-pulse analysis (default: 0.1 mV)
- **EPSP Threshold for 1st Amp**: Minimum amplitude for first response (default: 0 mV)

### Spatial Parameters
- **Column Width**: Cortical column width in µm (default: 200 µm)
- **Radial Step**: Step size for radial analysis (default: 50 µm)
- **Radial Max**: Maximum radius for analysis (default: 750 µm)
- **Symbol Radius**: Size of symbols in layout (default: 6 µm)

### Analysis Options
- **Manual Override**: Allows manual correction of automated response detection
- **Plot Modes**: Choose between bar graphs, box plots, or violin plots
- **Gamma Correction**: Adjust heat map contrast (default: 0.5)

## Data Export

The system exports comprehensive datasets including:

### Spatial Data
- Cell coordinates (X, Y, Z) in µm
- Layer and column assignments
- Distance metrics

### Response Data
- Connectivity status (binary)
- EPSP amplitudes (Amp1, Amp2, Amp3)
- Paired-pulse ratios (PPR)
- Triple-pulse ratios (TPR)
- Maximum depolarization values
- Direct activation flags

### Statistical Summaries
- Layer-wise connectivity percentages
- Radial connectivity profiles
- Amplitude statistics by layer and distance
- Cell counts and connection counts

## Advanced Features

### Layer Line Management
- **Store Layer Lines**: Manually define cortical layer boundaries
- **Auto Layer Lines**: Automatically generate layer boundaries using standardized thickness percentages
- **Redraw Layer Lines**: Restore previously defined boundaries
- **Automatic Layer Assignment**: Assigns cells to layers based on boundaries

#### Automatic Layer Line Generation
The new automatic layer line feature uses standardized cortical layer thickness percentages:
- L2/3-4 boundary: 15.975% from top
- L4-5 boundary: 15.560% additional
- L5-6 boundary: 30.083% additional
- L6-WM boundary: 38.381% additional

To use this feature:
1. Create your connectivity layout first
2. Place cursor A at the L1/L2-3 boundary (top)
3. Place cursor B at the L6/WM boundary (bottom)
4. Click "Auto layer lines" or use Macros → Auto-generate layer lines
5. The system will automatically calculate and draw all intermediate layer boundaries

### Manual Override System
- Correct automated response detection errors
- Handle direct depolarization artifacts
- Mark "trash" inputs to exclude from analysis
- Maintain override tables across sessions

### Integration with JT Load Waves
- Seamless data exchange with electrophysiology analysis tools
- Push/pull connectivity data
- Coordinate analysis workflows

## Output Files

Exported data is saved in Igor Text (.itx) format containing:
- All spatial coordinates and assignments
- Complete response datasets
- Statistical summaries
- Metadata and analysis parameters

## Version History

- **v01 (2021-2023)**: Initial development with comprehensive connectivity analysis
- **2021-05-06**: First functional version
- **2021-11-17**: Added layer and radial analysis with export features
- **2022-01-10**: Enhanced handling of postsynaptic cell positioning
- **2023-04-08**: Added third pulse analysis and manual override features
- **2023-09-06**: Implemented medial/anterior left-right analysis

## Requirements

- Igor Pro (version 6.0 or later recommended)
- JT Load Waves package (for electrophysiology integration)
- TIFF images with embedded stage coordinate metadata

## Support and Documentation

For detailed usage instructions and troubleshooting:
1. Check the extensive comments within the IPF file
2. Use the built-in help buttons in the control panel
3. Refer to the change log for feature updates and bug fixes

## Author

Developed by Jesper Sjöström, starting May 3, 2021.

## License

This software is provided as-is for research purposes. Please cite appropriately if used in publications.
